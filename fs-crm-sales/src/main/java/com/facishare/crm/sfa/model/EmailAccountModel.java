package com.facishare.crm.sfa.model;

import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.paas.metadata.api.IObjectData;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;
import java.util.Map;

public class EmailAccountModel {
    @Data
    public static class ByEmailArg {
        String from;
        List<String> to;
        List<String> cc;
    }

    @Data
    @EqualsAndHashCode(callSuper = false)
    public static class InitArg extends ByEmailArg {
        String action;
    }

    @Data
    public static class FindRelatedArg {
        String id;
        @JSONField(name = "related_api_name")
        @JsonProperty(value = "related_api_name")
        String relatedApiName;
        @JSONField(serialize = false)
        @JsonIgnore
        IObjectData emailAccount;
    }

    @Data
    public static class SyncEmailArg {
        @J<PERSON><PERSON>ield(name = "related_object")
        @JsonProperty(value = "related_object")
        Map<String, List<String>> relatedObject;
        Map<String, Object> content;
    }

    @Data
    public static class AssociateToArg {
        String id;
        @JSONField(name = "associate_to_id")
        @JsonProperty(value = "associate_to_id")
        String associateToId;
        @JSONField(name = "associate_to_api_name")
        @JsonProperty(value = "associate_to_api_name")
        String associateToApiName;
    }

    @Data
    @AllArgsConstructor
    public static class Result<T> {
        private String code;
        private String message;
        private T data;
    }
}
