package com.facishare.crm.sfa.predefine.service.Procurement;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.facishare.crm.sfa.lto.common.models.LtoFieldApiConstants;
import com.facishare.crm.sfa.lto.procurement.qianlima.QlmProcurementService;
import com.facishare.crm.sfa.predefine.SFAPreDefineObject;
import com.facishare.crm.sfa.predefine.action.AccountAddAction;
import com.facishare.crm.sfa.predefine.action.model.ProcurementAllocate;
import com.facishare.crm.sfa.predefine.exception.SFABusinessException;
import com.facishare.crm.sfa.predefine.exception.SFAErrorCode;
import com.facishare.crm.sfa.predefine.service.Procurement.module.*;
import com.facishare.crm.sfa.predefine.service.ProcurementAnalysisService;
import com.facishare.crm.sfa.predefine.service.ProcurementInfoService;
import com.facishare.crm.sfa.predefine.service.ProcurementRuleService;
import com.facishare.crm.sfa.predefine.service.model.procurement.ProcurementCallerStatusEnum;
import com.facishare.crm.sfa.predefine.service.model.procurement.ProcurementConstants;
import com.facishare.crm.sfa.utilities.constant.AccountConstants;
import com.facishare.crm.sfa.utilities.util.SFAI18NKeyUtil;
import com.facishare.crm.util.CommonSqlUtils;
import com.facishare.crm.util.ObjectFieldConstantsUtil;
import com.facishare.open.emailproxy.common.util.DateUtil;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.ParallelUtils;
import com.facishare.paas.appframework.config.ConfigValueType;
import com.facishare.paas.appframework.core.annotation.ServiceMethod;
import com.facishare.paas.appframework.core.annotation.ServiceModule;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.model.*;
import com.facishare.paas.appframework.core.predef.action.BaseObjectSaveAction;
import com.facishare.paas.appframework.core.predef.controller.StandardListHeaderController;
import com.facishare.paas.appframework.metadata.cache.RedissonServiceImpl;
import com.facishare.paas.appframework.metadata.dto.CommonFilterField;
import com.facishare.paas.appframework.metadata.layout.WebDetailLayout;
import com.facishare.paas.license.Result.ModuleInfoResult;
import com.facishare.paas.license.arg.QueryModuleArg;
import com.facishare.paas.license.common.LicenseContext;
import com.facishare.paas.license.http.LicenseClient;
import com.facishare.paas.license.pojo.ModuleInfoPojo;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.IObjectMappingRuleInfo;
import com.facishare.paas.metadata.api.ISelectOption;
import com.facishare.paas.metadata.api.action.IActionContext;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.dao.pg.mapper.metadata.SpecialTableMapper;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.facishare.paas.metadata.impl.ObjectData;
import com.facishare.paas.metadata.impl.describe.SelectOneFieldDescribe;
import com.facishare.paas.metadata.impl.describe.SelectOption;
import com.facishare.paas.metadata.impl.search.*;
import com.facishare.paas.metadata.impl.ui.layout.Layout;
import com.facishare.paas.metadata.support.CountryAreaService;
import com.facishare.paas.metadata.ui.layout.IComponent;
import com.facishare.paas.metadata.ui.layout.ILayout;
import com.facishare.paas.metadata.util.SpringUtil;
import com.facishare.restful.client.exception.FRestClientException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.json.JsonMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.fxiaoke.bi.industry.args.QueryBidListPageArg;
import com.fxiaoke.bi.industry.client.IndustryClient;
import com.fxiaoke.bi.industry.entity.BidListPageInfo;
import com.fxiaoke.bi.industry.entity.HttpResponseResult;
import com.fxiaoke.common.SqlEscaper;
import com.github.autoconf.ConfigFactory;
import com.google.common.base.Joiner;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RLock;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.facishare.crm.sfa.predefine.service.model.procurement.BiddingSubscriptionRulesConstants.*;
import static com.facishare.crm.sfa.predefine.service.model.procurement.ProcurementRule.PUBLISH_TIME;
import static com.facishare.crm.sfa.utilities.constant.objectdata.DbSystemRecord.LIFE_STATUS;

@ServiceModule("procurement")
@Component
@Slf4j
public class ProcurementService {

    private static final LicenseClient licenseClient = SpringUtil.getContext().getBean(LicenseClient.class);
    public static boolean isZsjEnvironment = false;

    private static final JsonMapper jsonMapper = new JsonMapper();
    private static final List<String> filterFields = Lists.newArrayList(BID_SUB_TYPE, KEYWORDS_FILTER, CALLER_TYPE, AREA_PROVINCE,
            ENTERPRISE_MATCH_FIELD, ENTERPRISE_MATCH_MODE, ENTERPRISE_NAME, PUBLISH_TIME, BUDGET_INTERVAL, KEYWORDS_MATCH_MODE,
            KEYWORDS_MATCH_FIELD, CALLER_NAMES, WINNER_NAMES, BID_METHOD, AREA_CITY_FILTER, BIDING_END_DATE_TIME, TENDER_END_DATE_TIME);

    @Autowired
    private ProcurementRuleService procurementRuleService;
    @Autowired
    private ProcurementAnalysisService procurementAnalysisService;
    @Autowired
    protected ServiceFacade serviceFacade;
    @Autowired
    protected IndustryClient industryClient;
    @Autowired
    private CountryAreaService countryAreaService;
    @Autowired
    private ProcurementInfoService procurementInfoService;
    @Autowired
    private QlmProcurementService qlmProcurementService;
    @Autowired
    SpecialTableMapper specialTableMapper;
    @Resource
    private RedissonServiceImpl redissonService;

    static {
        ConfigFactory.getConfig("fs-crm-sfa_rfm-config", config -> {
            isZsjEnvironment = config.getBool("is_zsj_env", false);
        });
    }

    @ServiceMethod("get_qlm_ai_subject_preview")
    public Procurement.QlmAiSubjectPreviewResult getQlmAiSubjectPreview(ServiceContext context, Procurement.QlmAiSubjectPreviewArg arg) {
        Procurement.QlmAiSubjectPreviewResult result = Procurement.QlmAiSubjectPreviewResult.builder().isSuccess(false).build();
        if (ObjectUtils.isEmpty(arg.getQlmId()) || ObjectUtils.isEmpty(arg.getObjectData()) ||
                ObjectUtils.isEmpty(arg.getObjectData().get("description_of_subject_matter")) ||
                ObjectUtils.isEmpty(arg.getObjectData().get("keywords"))){
            result.setErrorMsg(I18N.text(I18NKey.PARAM_EMPTY));
            return result;
        }
        QlmProcurementService.Account account = qlmProcurementService.getAccount(context.getTenantId());
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        // 通过千里马id反查出标讯信息
        JSONObject json = new JSONObject();
        json.put("accountKey", account.getAccountKey());
        json.put("dataId", Lists.newArrayList(Long.parseLong(arg.getQlmId())));
        QlmProcurementService.Result titleResult = qlmProcurementService.commonPost("/open/bigmodel/template/preview/loadTitle", json);
        if (titleResult.isSuccess()){
            JSONArray titleArray = titleResult.getData(JSONArray.class);
            if (!ObjectUtils.isEmpty(titleArray)){
                JSONObject data = titleArray.getJSONObject(0);
                result.setTitle(data.getString("title"));
                result.setCity(data.getString("city"));
                Long publishTime = data.getLong("publishTime");
                if (!ObjectUtils.isEmpty(publishTime)){
                    result.setPublishTime(simpleDateFormat.format(new Date(publishTime)));
                }
            }
        }
        // 每个租户只能同时有一个请求调用
        RLock lock = redissonService.tryLock(2, 2, TimeUnit.MINUTES, "sfa_ProcurementInfoObj_qlm_ai_" + context.getTenantId());
        if (null == lock) {
            log.warn("redis lock error");
            try {
                Thread.sleep(2000);
            } catch (Exception e) {
                // 忽略异常
            }
            result.setErrorMsg(I18N.text("sfa.procurement.qlm.frequent.operations"));
            return result;
        }
        try{
            List<String> keywordsList = new ArrayList<>();
            JSONArray keyWordsJson = JSONObject.parseObject(arg.getObjectData().get("keywords").toString(), JSONArray.class);
            keyWordsJson.forEach(item -> keywordsList.addAll(Arrays.asList(item.toString().split(" "))));
            json = new JSONObject();
            json.put("businessContent", arg.getObjectData().get("description_of_subject_matter"));
            json.put("excludeBusinessContent", arg.getObjectData().get("description_of_not_subject_matter"));
            json.put("precautions", arg.getObjectData().get("subscription_requirements"));
            json.put("keywords", String.join(",", keywordsList));
            json.put("accountKey", account.getAccountKey());
            json.put("dataId", Long.parseLong(arg.getQlmId()));
            QlmProcurementService.Result qlmRes = qlmProcurementService.commonPost("/open/bigmodel/template/related/preview", json, 60L * 1000L);
            if (qlmRes.isSuccess()){
                result.setIsSuccess(true);
                result.setContent(qlmRes.getData());
                result.setIsValid(qlmRes.getData().indexOf("是") == 0); // ignoreI18n
            }else {
                result.setErrorMsg(qlmRes.getMsg());
            }
        }catch (NumberFormatException e){
            result.setErrorMsg(I18N.text(I18NKey.PARAM_EMPTY));
        } catch (Exception e){
            log.error("get_qlm_ai_subject_preview erro!", e);
            throw new ValidateException(I18N.text("paas.sf.network_timeout"));
        } finally {
            redissonService.unlock(lock);
        }
        return result;
    }

    @ServiceMethod("test_nameSimilarity")
    public QlmProcurementService.Result test_nameSimilarity(ServiceContext context, JSONObject json) {
        double nameSimilarity = QlmProcurementService.similarity(json.getString("procurementInfoName"), json.getString("accountName"));
        QlmProcurementService.Result result = new QlmProcurementService.Result();
        result.setCode(200);
        result.setData(String.valueOf(nameSimilarity));
        return result;
    }

    @ServiceMethod("query_by_subscriber")
    public Procurement.PreviewPage queryBySubscriber(ServiceContext context, Procurement.PreviewParam param) {
        if (param.getParam() == null || param.getParam().isEmpty()) {
            throw new ValidateException(I18N.text(SFAI18NKeyUtil.THE_QUERY_CRITERIA_CANNOT_BE_EMPTY));
        }
        if (param.getPageSize() == null) {
            param.setPageSize(10);
        }
        if (param.getPageNo() == null) {
            param.setPageNo(1);
        }
        if (ProcurementUtils.QLM.equals(param.getRecordType())) {
            return procurementInfoService.queryBySubscriberForQlm(context.getTenantId(), param);
        } else {
            QueryBidListPageArg requestParam = buildQueryArg(context, param);
            requestParam.setEndPublishDate(System.currentTimeMillis());
            requestParam.setStartPublishDate(requestParam.getEndPublishDate() - 7 * 24 * 60 * 60 * 1000L);
            return procurementInfoService.queryBySubscriberForZl(context.getTenantId(), requestParam, param);
        }
    }

    @ServiceMethod("sync_qlm_consumption_records")
    public QlmProcurementService.Result syncQlmConsumptionRecords(ServiceContext context) {
        procurementInfoService.syncQlmConsumptionRecords(context.getTenantId(), LocalDate.now());
        QlmProcurementService.Result result = new QlmProcurementService.Result();
        result.setCode(200);
        return result;
    }

    @ServiceMethod("receiver_qlm")
    public QlmProcurementService.Result receiverQlmCreate(ServiceContext context) {
        String tenantId = context.getTenantId();
        ActionContext actionContext = new ActionContext(RequestContext.builder().tenantId(tenantId).user(User.systemUser(tenantId)).build(), SFAPreDefineObject.ProcurementInfo.getApiName(), null);
        QlmProcurementService.Result result = new QlmProcurementService.Result();
        result.setCode(200);
        ParallelUtils.ParallelTask task = ParallelUtils.createParallelTask();
        task.submit(() -> procurementInfoService.syncQlmProcurementInfo(actionContext));
        task.submit(() -> procurementInfoService.syncQlmConsumptionRecords(context.getTenantId(), LocalDate.now()));
        task.run();
        return result;
    }

    /**
     * 千里马订阅器数据补偿
     * 参数：{"startTime":"2025-03-27 10:35:00","endTime":"2025-03-27 15:40:00","tenantId":"590061"}
     */
    @ServiceMethod("qlmDataCompensation")
    public QlmProcurementService.Result qlmDataCompensation(ServiceContext context, Map<String, String> arg) {
        QlmProcurementService.Result result = new QlmProcurementService.Result();
        result.setCode(200);
        if (ObjectUtils.isEmpty(arg.get("startTime")) || ObjectUtils.isEmpty(arg.get("endTime")) || ObjectUtils.isEmpty(arg.get("tenantId"))) {
            result.setMsg("arg is null!");
            return result;
        }
        String tenantId = arg.get("tenantId");
        ActionContext actionContext = new ActionContext(RequestContext.builder().tenantId(tenantId).user(User.systemUser(tenantId)).build(), SFAPreDefineObject.ProcurementInfo.getApiName(), null);
        QlmProcurementService.Account account = qlmProcurementService.getAccount(context.getTenantId());
        ParallelUtils.ParallelTask task = ParallelUtils.createParallelTask();
        task.submit(() -> procurementInfoService.syncQlmProcurementPackage(actionContext, account, arg.get("startTime"), arg.get("endTime")));
        task.run();
        return result;
    }

    @ServiceMethod("qlm_buy_data")
    public QlmProcurementService.Result qlmBuyData(ServiceContext context,Map<String,String> arg) {
        ParallelUtils.ParallelTask task = ParallelUtils.createParallelTask();
        task.submit(() -> procurementInfoService.syncQlmBuyData(context.getTenantId(), arg.get("data_id"))).run();
        QlmProcurementService.Result result = new QlmProcurementService.Result();
        result.setCode(200);
        return result;
    }

    @ServiceMethod("get_qlm_area")
    public JSONObject getQlmArea(ServiceContext context) {
        JSONObject res = new JSONObject();
        res.put("province", JSON.parseArray(QlmProcurementService.getProvince()));
        res.put("city", JSON.parseArray(QlmProcurementService.getCity()));
        return res;
    }

    @ServiceMethod("get_qlm_token")
    public QlmProcurementService.Result getToken(ServiceContext context) {
        String tenantId = context.getTenantId();
        QlmProcurementService.Account account = qlmProcurementService.getAccount(tenantId);
        if (account == null) {
            throw new ValidateException(I18N.text(SFAI18NKeyUtil.NO_QIANLIMA_ACCOUNT_FOUND));
        }
        JSONObject json = new JSONObject();
        json.put("accountKey", account.getAccountKey());
        json.put("accountSecret", account.getAccountSecret());
        QlmProcurementService.Result result = qlmProcurementService.commonPost("/open/account/login", json);
        if (!result.isSuccess()) {
            throw new ValidateException(result.getMsg());
        }
        return result;
    }

    @ServiceMethod("qlm_crypt")
    public JSONObject qlmCrypt(ServiceContext context, JSONObject json) {
        String plaintext = json.getString("plaintext");
        JSONObject result = new JSONObject();
        result.put("ciphertext", qlmProcurementService.cryptStr(plaintext));
        return result;
    }

    @ServiceMethod("suspected_opportunity_rule_set")
    public SuspectedOpportunity.Rule setSuspectedOpportunityRule(ServiceContext context, SuspectedOpportunity.Rule rule) {
        String tenantId = context.getTenantId();
        serviceFacade.upsertTenantConfig(User.systemUser(tenantId), SuspectedOpportunity.RULE_KEY, JSON.toJSONString(rule), ConfigValueType.JSON);
        return rule;
    }

    @ServiceMethod("suspected_opportunity_rule_get")
    public SuspectedOpportunity.Rule getSuspectedOpportunityRule(ServiceContext context) {
        String tenantId = context.getTenantId();
        String json = serviceFacade.findTenantConfig(User.systemUser(tenantId), SuspectedOpportunity.RULE_KEY);
        if (StringUtils.isEmpty(json)) {
            return new SuspectedOpportunity.Rule();
        }
        // 兼容老配置 为空时是true
        SuspectedOpportunity.Rule rule = JSON.parseObject(json, SuspectedOpportunity.Rule.class);
        if (Boolean.FALSE.equals(rule.getUseAccount())){
            return rule;
        }
        rule.setUseAccount(true);
        return rule;
    }

    @ServiceMethod("get_procurement_license")
    public ProcurementLicense.Result queryProcurementLicense(ServiceContext context) {
        Integer quotaTotal = procurementRuleService.getProcurementLicenseTotal(context.getTenantId(), context.getUser());
        Integer quotaUsed = procurementAnalysisService.getProcurementAnalysisCount(context.getTenantId());
        return ProcurementLicense.Result.builder().quotaTotal(quotaTotal).quotaUsed(quotaUsed).quotaRemain(quotaTotal - quotaUsed).build();
    }

    @ServiceMethod("get_procurement_search")
    public ProcurementLicense.ProcurementExistResult queryProcurementSearch(ServiceContext context) {
        ProcurementLicense.ProcurementExistResult result = new ProcurementLicense.ProcurementExistResult();
        result.setHasProcurement(getLicense(context, "bidding_info_manage_app"));
        return result;
    }

    @ServiceMethod("queryBidCallRecordLog")
    public Map<String, Object> queryBidCallRecordLog(ServiceContext context, ProcurementLicense.ProcurementRecordLogArg arg) {
        Map<String, Object> map = new HashMap<>();
        try {
            map = industryClient.queryBidCallRecordLog(JSON.toJSONString(arg));
        } catch (FRestClientException e) {
            throw new RuntimeException(e);
        }
        return map;
    }

    /**
     * 获取两期招投标license
     *
     * @param context
     * @return
     */
    @ServiceMethod("get_procurement")
    public ProcurementLicense.ProcurementExistResult queryProcurement(ServiceContext context) {
        ProcurementLicense.ProcurementExistResult res = new ProcurementLicense.ProcurementExistResult();
        // 招商局屏蔽所有标讯的License
        if (isZsjEnvironment) {
            return res;
        }
        // 查询License
        boolean procurement_customer_acquisition_app = getLicense(context, "procurement_customer_acquisition_app");
        boolean qlmAiLicense = getLicense(context, "bidding_info_manage_qianlima_subscription_ai_plugin_app");
        boolean qlmASubjectMatterLicense = getLicense(context, "bidding_analysis_qianlima_rule_AIplugin_app");
        boolean ZL = getLicense(context, "bidding_info_manage_app");
        boolean QLM = getLicense(context, "bidding_info_manage_qianlima_app")
                || getLicense(context, "bidding_info_manage_qianlima_200_app")
                || getLicense(context, "bidding_info_manage_qianlima_200plus_app");
        // 根据License给前端返回标识
        res.setHasProcurement(procurement_customer_acquisition_app);
        res.setMarketAnalysis(procurement_customer_acquisition_app);
        res.setTransferRules(procurement_customer_acquisition_app);
        if (ZL || QLM) {
            res.setAutoComparisonRules(true);
        }
        res.setHasProcurementSecond(ZL);
        res.setHasProcurementZl(ZL);
        res.setHistoricalImport(ZL);
        res.setHasProcurementQlm(QLM);
        res.setHasQlmAiLicense(qlmAiLicense);
        res.setHasQlmAiSubjectMatterLicense(qlmASubjectMatterLicense);
        return res;
    }


    @ServiceMethod("get_bidding_rule_filter")
    public StandardListHeaderController.Result getBiddingRuleFilter(ServiceContext context, BiddingSubscriptionRule.Arg arg) {
        StandardListHeaderController.Result result = new StandardListHeaderController.Result();
        IObjectData subData = serviceFacade.findObjectData(context.getUser(), arg.getDataId(), SFAPreDefineObject.BiddingSubscription.getApiName());
        if (subData == null) {
            return result;
        }
        String ruleId = subData.get(RULE_ID) == null ? "" : String.valueOf(subData.get(RULE_ID));
        IObjectData ruleData = serviceFacade.findObjectData(context.getUser(), ruleId, SFAPreDefineObject.BiddingSubscriptionRules.getApiName());
        IObjectDescribe subDescribe = serviceFacade.findObject(context.getTenantId(), SFAPreDefineObject.BiddingSubscription.getApiName());
        IObjectDescribe objectDescribe = rebuildDescribe(context, subDescribe, ruleData);
        result.setObjectDescribe(ObjectDescribeDocument.of(objectDescribe));
        List<CommonFilterField.FilterField> filterFields = getFilterFields();
        result.setFilterFields(filterFields);
        result.setListSingleExposed(1);
        return result;
    }


    @ServiceMethod("chooseProcurement")
    public ProcurementAllocate.Result chooseProcurement(ServiceContext context, ProcurementAllocate.BulkArg args) {
        ProcurementAllocate.Result result = new ProcurementAllocate.Result();
        StringBuilder message = new StringBuilder();
        for (ProcurementAllocate.Arg arg : args.getDataList()) {
            try {
                procurementInfoService.allocateProcurement2DB(new ActionContext(context.getRequestContext(), SFAPreDefineObject.ProcurementInfo.getApiName(), null), arg);
            } catch (Exception e) {
                log.error("chooseProcurement:{}", arg, e);
                message.append(arg.getId()).append(":").append(e.getMessage()).append(";");
            }
        }
        result.setMessage(message.toString());
        result.setSuccess(message.length() == 0);
        return result;
    }

    @ServiceMethod("get_bidding_rule_count")
    public BiddingSubscriptionRule.R getCountByBiddingRule(ServiceContext context, BaseObjectSaveAction.Arg arg) {
        QueryBidListPageArg queryBidListPageArg = buildQueryArg(context, arg.getObjectData());
        BidListPageInfo industryList = getIndustryList(queryBidListPageArg);
        return BiddingSubscriptionRule.R.builder().totalCount(industryList.getTotal()).build();
    }

    private QueryBidListPageArg buildQueryArg(ServiceContext context, ObjectDataDocument objectData) {
        Procurement.PreviewParam param = new Procurement.PreviewParam();
        param.setPageNo(1);
        param.setPageSize(10);
        param.setParam(objectData);
        return buildQueryArg(context, param);
    }

    private QueryBidListPageArg buildQueryArg(ServiceContext context, Procurement.PreviewParam param) {
        QueryBidListPageArg bidListPageArg = QueryBidListPageArg.builder()
                .tenantId(context.getTenantId())
                .employeeId(context.getUser().getUserId())
                .terminalType(context.getClientInfo())
                .limit(param.getPageSize())
                .page(param.getPageNo())
                .source("ZhiLiao")
                .matchMode(2)
                .matchType(2)
                .keywords(Lists.newArrayList())
                .build();
        IObjectData objectData = param.getParam().toObjectData();
        Object conditions = objectData.get(CONDITIONS);

        Map<String, List<String>> rangeData2Map = new HashMap<>();
        if (conditions != null) {
            rangeData2Map = procurementRuleService.userRangeData2Map(conditions);
        }

        if (objectData.get(KEYWORDS) != null) {
            bidListPageArg.setKeywords(Lists.newArrayList(StringUtils.split(objectData.get(KEYWORDS).toString(), " ")));
        }
        List<String> bidSubTypes = rangeData2Map.get(BID_SUB_TYPE);
        if (CollectionUtils.isNotEmpty(bidSubTypes)) {
            bidListPageArg.setBidProcesses(bidSubTypes.stream().map(Integer::valueOf).collect(Collectors.toList()));
        }

        List<String> areProvinces = rangeData2Map.get(AREA_PROVINCE);
        if (CollectionUtils.isNotEmpty(areProvinces)) {
            bidListPageArg.setProvince(areProvinces);
        }
        List<String> callerTypes = rangeData2Map.get(CALLER_TYPE);
        if (CollectionUtils.isNotEmpty(callerTypes)) {
            List<Integer> types = callerTypes.stream().map(Integer::valueOf).collect(Collectors.toList());
            bidListPageArg.setCallerOrgBaseTypeName(types);
        }
        List<String> publishTime = rangeData2Map.get(PUBLISH_TIME);
        if (CollectionUtils.isNotEmpty(publishTime)) {
            bidListPageArg.setStartPublishDate(Long.parseLong(publishTime.get(0)));
            bidListPageArg.setEndPublishDate(Long.parseLong(publishTime.get(1)));
        }
        List<String> areaCity = rangeData2Map.get(AREA_CITY);
        if (areaCity != null) {
            bidListPageArg.setCity(areaCity);
        }

        String keywordsMatchMode = objectData.get(KEYWORDS_MATCH_MODE, String.class);
        if (keywordsMatchMode != null) {
            bidListPageArg.setMatchMode(Integer.parseInt(keywordsMatchMode));
        }

        String keywordsMatchField = objectData.get(KEYWORDS_MATCH_FIELD, String.class);
        if (keywordsMatchField != null) {
            bidListPageArg.setMatchType(Integer.parseInt(keywordsMatchField));
        }

        List<String> tenderEndDate = rangeData2Map.get(BIDING_END_DATE_TIME);
        if (CollectionUtils.isNotEmpty(tenderEndDate)) {
            bidListPageArg.setStartTenderDate(Long.parseLong(tenderEndDate.get(0)));
        }
        List<String> bidingEndDate = rangeData2Map.get(TENDER_END_DATE_TIME);
        if (CollectionUtils.isNotEmpty(bidingEndDate)) {
            bidListPageArg.setStartSignupDate(Long.parseLong(bidingEndDate.get(0)));
        }
        String budgetInterval = objectData.get(BUDGET_INTERVAL, String.class);
        if (StringUtils.isNotBlank(budgetInterval)) {
            String[] split = budgetInterval.split(",");
            if (split.length == 2) {
                bidListPageArg.setLowBudgetFunds(split[0]);
                bidListPageArg.setHighBudgetFunds(split[1]);
            }
        }
        if (objectData.get(IS_ENTERPRISE_SUB) == null || !(Boolean) objectData.get(IS_ENTERPRISE_SUB)) {
            return bidListPageArg;
        }
        //精准匹配
        boolean matchMode = "1".equals(objectData.get(ENTERPRISE_MATCH_MODE, String.class));

        List<String> enterpriseNames = getEnterpriseName(context.getTenantId(), objectData);
        String matchField = objectData.get(ENTERPRISE_MATCH_FIELD, String.class);
        if (CollectionUtils.isNotEmpty(enterpriseNames) && matchField != null) {
            String enterpriseName = Joiner.on(",").join(enterpriseNames);
            if (!matchMode) {
                enterpriseName = "#" + enterpriseName;
            }
            if ("1".equals(matchField)) {
                bidListPageArg.setCallerName(enterpriseName);
            } else {
                bidListPageArg.setWinnerName(enterpriseName);
            }
        }
        return bidListPageArg;
    }

    private List<String> getEnterpriseName(String tenantId, IObjectData objectData) {
        String enterpriseName = objectData.get(ENTERPRISE_NAME, String.class);
        String objectIds = objectData.get(ENTERPRISE_RELATED_ID, String.class);
        if (StringUtils.isNotBlank(enterpriseName) && StringUtils.isEmpty(objectIds)) {
            return new ArrayList<>(Arrays.asList(enterpriseName.split(",")));
        }
        String[] split = objectIds.split(",");
        List<String> ids = Arrays.asList(split);
        Object objectApiName = objectData.get(ENTERPRISE_RELATED_OBJ);
        if (objectApiName == null || StringUtils.isBlank(objectApiName.toString())) {
            return Collections.emptyList();
        }
        return getObjectDataNamesActivated(tenantId, ids, String.valueOf(objectApiName));
    }

    private List<CommonFilterField.FilterField> getFilterFields() {
        List<CommonFilterField.FilterField> filterFields = new ArrayList<>();
        filterFields.add(CommonFilterField.FilterField.builder().fieldName(KEYWORDS).isShow(true).build());
        filterFields.add(CommonFilterField.FilterField.builder().fieldName("bid_processes").isShow(true).build());
        filterFields.add(CommonFilterField.FilterField.builder().fieldName("caller_names").isShow(true).build());
        filterFields.add(CommonFilterField.FilterField.builder().fieldName("winner_names").isShow(true).build());
        filterFields.add(CommonFilterField.FilterField.builder().fieldName("caller_type").isShow(true).build());
        filterFields.add(CommonFilterField.FilterField.builder().fieldName("area_province").isShow(true).build());
        filterFields.add(CommonFilterField.FilterField.builder().fieldName("publish_time").isShow(true).build());
        filterFields.add(CommonFilterField.FilterField.builder().fieldName("budget_interval").isShow(true).build());
        filterFields.add(CommonFilterField.FilterField.builder().fieldName("match_mode").isShow(true).build());
        filterFields.add(CommonFilterField.FilterField.builder().fieldName("match_type").isShow(true).build());
        filterFields.add(CommonFilterField.FilterField.builder().fieldName(ENTERPRISE_MATCH_MODE).isShow(true).build());
        filterFields.add(CommonFilterField.FilterField.builder().fieldName(ENTERPRISE_MATCH_FIELD).isShow(true).build());
        filterFields.add(CommonFilterField.FilterField.builder().fieldName(BID_METHOD).isShow(true).build());
        filterFields.add(CommonFilterField.FilterField.builder().fieldName(AREA_CITY_FILTER).isShow(true).build());
        filterFields.add(CommonFilterField.FilterField.builder().fieldName(TENDER_END_DATE_TIME).isShow(true).build());
        filterFields.add(CommonFilterField.FilterField.builder().fieldName(BIDING_END_DATE_TIME).isShow(true).build());
        return filterFields;
    }

    private boolean getLicense(ServiceContext context, String appId) {
        return getLicense(context.getUser(), appId);
    }

    public boolean getLicense(User user, String appId) {
        LicenseContext licenseContext = new LicenseContext();
        licenseContext.setAppId(appId);
        licenseContext.setTenantId(user.getTenantId());
        licenseContext.setUserId(user.getUpstreamOwnerIdOrUserId());
        QueryModuleArg arg = new QueryModuleArg();
        arg.setLicenseContext(licenseContext);
        ModuleInfoResult result = licenseClient.queryModule(arg);
        List<ModuleInfoPojo> modules = result.getResult();
        if (CollectionUtils.isNotEmpty(modules) && modules.stream().anyMatch(module -> Objects.equals(module.getModuleCode(), appId))) {
            return true;
        }
        return false;
    }

    public Map<String, Object> getAiResources(User user) {
        Map<String, Object> retMap = new HashMap<>();
        retMap.put("show", false);
        retMap.put("all", 0);
        retMap.put("use", 0);
        try {
            QlmProcurementService.Account account = qlmProcurementService.getAccount(user.getTenantId());
            JSONObject json = new JSONObject();
            json.put("accountKey", account.getAccountKey());
            QlmProcurementService.Result result = qlmProcurementService.commonPost("/open/account/source/bigModel", json);
            if (!result.isSuccess()) {
                return retMap;
            }
            JSONArray data = result.getData(JSONArray.class);
            for (int i = 0; i < data.size(); i++) {
                JSONObject resourcesJsonObject = data.getJSONObject(i);
                if (!"BIG_MODEL_RELATED".equals(resourcesJsonObject.getString("resources"))) {
                    continue;
                }
                long total = resourcesJsonObject.getLong("total");
                long balance = resourcesJsonObject.getLong("balance");
                retMap.put("all", total);
                retMap.put("use", total - balance);
                retMap.put("show", total > 0);
                break;
            }
        } catch (Exception e) {
            log.error("getAiResources error", e);
        }
        return retMap;
    }

    @ServiceMethod("send_add_result")
    public ProcurementLicense.SendMsgResult send(ServiceContext context, ProcurementLicense.SendMsgArg arg) {
        String procurementId = arg.getProcurementId();
        String dataId = arg.getDataId();
        procurementAnalysisService.updateProcurementAccount(context, procurementId, dataId);
        procurementAnalysisService.sendMsgBySingle(new ActionContext(context.getRequestContext(), SFAPreDefineObject.ProcurementAccount.getApiName(), null), dataId, procurementId);
        IObjectDescribe describe = serviceFacade.findObject(context.getTenantId(), SFAPreDefineObject.ProcurementAnalysis.getApiName());
        IObjectData accountById = procurementAnalysisService.getProcurementAccountById(procurementId, new ActionContext(context.getRequestContext(), "", ""));
        String analysisId = accountById.get("procurement_analysis_id") == null ? "" : accountById.get("procurement_analysis_id").toString();
        IObjectData analysisById = procurementAnalysisService.getProcurementAnalysisById(new ActionContext(context.getRequestContext(), "", ""), analysisId);
        procurementAnalysisService.addAnalysisOperateSingleLog(context, describe, analysisById);
        return ProcurementLicense.SendMsgResult.builder().success(true).build();
    }


    @ServiceMethod("transfer_all")
    public ProcurementLicense.SendMsgResult transferAll(ServiceContext context, ProcurementTransferAllModule.Arg arg) {
        String analysisId = arg.getProcurementAnalysisId();
        String recordType = arg.getRecordType();
        procurementAnalysisService.sendAll(new ActionContext(context.getRequestContext(), SFAPreDefineObject.ProcurementAccount.getApiName(), null), analysisId, recordType);
        IObjectData analysisById = procurementAnalysisService.getProcurementAnalysisById(new ActionContext(context.getRequestContext(), SFAPreDefineObject.ProcurementAccount.getApiName(), null), analysisId);
        IObjectDescribe describe = serviceFacade.findObject(context.getTenantId(), SFAPreDefineObject.ProcurementAnalysis.getApiName());
        procurementAnalysisService.addAnalysisOperateAllLog(context, describe, analysisById);
        return ProcurementLicense.SendMsgResult.builder().success(true).build();
    }


    public BidListPageInfo getIndustryList(QueryBidListPageArg bidListPageArg) {
        HttpResponseResult<BidListPageInfo> result = new HttpResponseResult<>();
        try {
            result = industryClient.queryBidListPageByKeywords(bidListPageArg);
            log.warn("queryBidListPageByKeywords arg:{} result:{}", bidListPageArg, result);
        } catch (FRestClientException e) {
            log.error("get industryList error", e);
        }
        if ("401".equals(result.getErrorCode()) || (200 != result.getStatus() && StringUtils.isNotBlank(result.getErrorMessage()))) {
            throw new ValidateException(result.getErrorMessage());
        }
        return result.getResult();
    }

    public IObjectData getMappingAndTransferData(List<IObjectMappingRuleInfo> ruleList, IObjectData sourceDate, User user) {

        if (CollectionUtils.isEmpty(ruleList)) {
            log.warn("procurementEnterprise_transfer--transfer，映射关系为空");
            throw new ValidateException(I18N.text(SFAI18NKeyUtil.SFA_PARAMET_ERERROR));
        }
        //
        IObjectData newData = new ObjectData();
        ruleList.stream().forEach(rule -> {
            List<Map> fieldMappingList = (List<Map>) rule.get("field_mapping");
            fieldMappingList.stream().forEach(fieldMapping -> {
                String source_field_api_name = fieldMapping.get("source_field_api_name").toString();
                if (sourceDate.containsField(source_field_api_name)) {
                    String target_field_api_name = fieldMapping.get("target_field_api_name").toString();
                    if (fieldMapping.containsKey("option_mapping")) {
                        List<Map> optionMappingList = (List<Map>) fieldMapping.get("option_mapping");
                        optionMappingList.stream().forEach(optionMapping -> {
                            if (sourceDate.get(source_field_api_name) != null && optionMapping.get("source_option").equals(sourceDate.get(source_field_api_name).toString())) {
                                newData.set(target_field_api_name, optionMapping.get("target_option"));
                            }
                        });
                    } else {
                        newData.set(target_field_api_name, sourceDate.get(source_field_api_name));
                    }
                }
            });
        });
        //设置来源
        newData.set(AccountConstants.Field.FROM, AccountAddAction.AccountFrom.PROCUREMENT_ENTERPRISE_TRANSFER.getValue());
        return newData;
    }

    public Long getDaysByMillis(String dateLongMillis) {
        if (StringUtils.isBlank(dateLongMillis)) {
            return null;
        }
        long l = Long.parseLong(dateLongMillis) - System.currentTimeMillis();
        if (l < 0L) {
            return null;
        }
        return l /= 86400000L;
    }

    public String splitNames(List<String> products) {
        return StringUtils.join(products, "、");
    }

    public String getArea(Object province, Object city) {
        StringBuilder sb = new StringBuilder();
        if (province != null && StringUtils.isNotBlank(province.toString())) {
            sb.append(province);
        }
        if (city != null && StringUtils.isNotBlank(city.toString())) {
            sb.append(city);
        }
        return StringUtils.isBlank(sb.toString()) ? null : sb.toString();
    }

    public void removeHiddenComponent(LayoutDocument layoutDocument) {
        ILayout layout = new Layout(layoutDocument);
        List<IComponent> components;
        List<String> removeComponentApiNameList = Lists.newArrayList();
        try {
            components = layout.getComponents();
            if (components == null) {
                return;
            }
            removeComponentApiNameList = components.stream().filter(IComponent::isHidden).map(IComponent::getName).collect(Collectors.toList());
        } catch (MetadataServiceException e) {
        }
        WebDetailLayout.of(layout).removeComponents(removeComponentApiNameList);
    }

    /**
     * 逻辑与 {@link #getCallerStatus(Long, Long)} 方法相同
     * @param bidingEndTime 投标截止日期
     * @param tenderEndTime 获取标书截止日期
     * @return 招投标状态
     */
    public String getCallerStatus(String bidingEndTime, String tenderEndTime) {
        long now = new Date().getTime();
        if (StringUtils.isNotBlank(tenderEndTime)) {
            Date date = DateUtil.formatStringToDate(bidingEndTime, "yyyy-MM-dd HH:mm:ss");
            if (date != null && date.getTime() >= now) {
                return ProcurementCallerStatusEnum.BIDING.getValue();
            }
        }

        if (StringUtils.isNotBlank(bidingEndTime)) {
            Date date = DateUtil.formatStringToDate(bidingEndTime, "yyyy-MM-dd HH:mm:ss");
            if (date != null && date.getTime() >= now) {
                return ProcurementCallerStatusEnum.TENDERING.getValue();
            } else if (date != null && date.getTime() < now) {
                return ProcurementCallerStatusEnum.TENDERED.getValue();
            }
        }

        return ProcurementCallerStatusEnum.OTHERS.getValue();
    }

    /**
     * 计算招标状态实际逻辑：<br/>
     * (当 一级公告类型 是 招标类型 时进行计算) 调用方法前判断<br/>
     * 如果 获取标书截止日期 不为空 并且 获取标书截止日期 大于等于现在 -> 标书获取中<br/>
     * 如果 投标截止日期 不为空 并且 投标截止日期 大于等于现在 -> 投标中<br/>
     * 如果 投标截止日期 不为空 并且 投标截止日期 小于现在 -> 投标已截止<br/>
     * 如果以上都不成立 -> 其他<br/>
     * <br/>
     * @param bidingEndTime 投标截止日期
     * @param tenderEndTime 获取标书截止日期
     * @return 招标状态
     */
    public String getCallerStatus(Long bidingEndTime, Long tenderEndTime) {
        String status;
        long now = new Date().getTime();
        if (tenderEndTime != null
                && tenderEndTime >= now) {
            status = ProcurementCallerStatusEnum.BIDING.getValue();
        } else if (bidingEndTime != null
                && bidingEndTime >= now) {
            status = ProcurementCallerStatusEnum.TENDERING.getValue();
        } else if (bidingEndTime != null
                && bidingEndTime < now) {
            status = ProcurementCallerStatusEnum.TENDERED.getValue();
        } else {
            status = ProcurementCallerStatusEnum.OTHERS.getValue();
        }
        return status;
    }


    private IObjectDescribe rebuildDescribe(ServiceContext context, IObjectDescribe document, IObjectData ruleData) {

        List<IFieldDescribe> fieldDescribes = document.getFieldDescribes();
        List<IFieldDescribe> newFieldDescribes = new ArrayList<>();
        fieldDescribes.removeIf(field -> !filterFields.contains(field.getApiName()));
        boolean isEnterpriseSub = ruleData.get(IS_ENTERPRISE_SUB) != null && (Boolean) ruleData.get(IS_ENTERPRISE_SUB);
        Object conditions = ruleData.get(CONDITIONS);
        Map<String, List<String>> rangeData2Map = new HashMap<>();
        if (conditions != null) {
            rangeData2Map = procurementRuleService.userRangeData2Map(conditions);
        }

        for (String filterField : filterFields) {
            Object filterValue = ruleData.get(filterField);
            // 配置条件为空，则从描述中移除该字段。keywords_filter 除外
            if ((filterValue == null || StringUtils.isBlank(filterValue.toString()))
                    && !rangeData2Map.containsKey(filterField)
                    && !KEYWORDS_FILTER.equals(filterField)
                    && !BUDGET_INTERVAL.equals(filterField)
                    && !PUBLISH_TIME.equals(filterField)
                    && !CALLER_NAMES.equals(filterField)
                    && !WINNER_NAMES.equals(filterField)
                    && !AREA_CITY_FILTER.equals(filterField)) {
                String filterFieldTemp = filterField;
                fieldDescribes.removeIf(field -> field.getApiName().equals(filterFieldTemp));
                continue;
            }

            for (IFieldDescribe fieldDescribe : fieldDescribes) {
                if (!fieldDescribe.getApiName().equals(filterField)) {
                    continue;
                }
                if (AREA_CITY_FILTER.equals(fieldDescribe.getApiName())) {
                    filterField = AREA_CITY;
                }
                if (isAlwaysShowFields(fieldDescribe.getApiName())) {
                    newFieldDescribes.add(fieldDescribe);
                    continue;
                }

                List<String> list = new ArrayList<>();
                if (fieldDescribe instanceof SelectOneFieldDescribe && !CALLER_NAMES.equals(filterField) && !WINNER_NAMES.equals(filterField)) {
                    SelectOneFieldDescribe selectOneFieldDescribe = (SelectOneFieldDescribe) fieldDescribe;
                    if (rangeData2Map.containsKey(filterField)) {
                        list.addAll(rangeData2Map.get(filterField));
                        selectOneFieldDescribe.setDefaultValue(rangeData2Map.get(filterField));
                    } else if (filterValue instanceof String) {
                        list.add(filterValue.toString());
                    }
                    List<ISelectOption> options = selectOneFieldDescribe.getSelectOptions();
                    if (AREA_CITY.equals(filterField)) {
                        List<Map<String, String>> cityMaps = countryAreaService.batchQueryNameByCode(CommonSqlUtils.convert2ActionContext(context), "city", new HashSet<>(list));
                        if (CollectionUtils.isNotEmpty(cityMaps)) {
                            for (Map<String, String> cityMap : cityMaps) {
                                options.add(new SelectOption(cityMap.get("label"), cityMap.get("value"), ""));
                            }
                        }
                    } else {
                        options.removeIf(option -> !list.contains(option.getValue()));
                    }
                    selectOneFieldDescribe.setSelectOptions(options);
                    selectOneFieldDescribe.setApiName(getSearchApiName(selectOneFieldDescribe.getApiName()));
                    if (CollectionUtils.isNotEmpty(options)) {
                        List<String> optionValues = options.stream().map(ISelectOption::getValue).collect(Collectors.toList());
                        if (selectOneFieldDescribe.getDefaultValue() == null ||
                                (selectOneFieldDescribe.getDefaultValue() instanceof String && !optionValues.contains(selectOneFieldDescribe.getDefaultValue().toString()))) {
                            selectOneFieldDescribe.setDefaultValue(options.get(0).getValue());
                        }
                        newFieldDescribes.add(selectOneFieldDescribe);
                    }
                }

                if (KEYWORDS_FILTER.equals(filterField)) {
                    filterValue = ruleData.get(KEYWORDS);
                    String[] values = filterValue.toString().split(" ");
                    SelectOneFieldDescribe selectOneFieldDescribe = new SelectOneFieldDescribe();
                    List<ISelectOption> options = selectOneFieldDescribe.getSelectOptions();
                    for (String value : values) {
                        options.add(new SelectOption(value, value, ""));
                    }
                    selectOneFieldDescribe.setSelectOptions(options);
                    selectOneFieldDescribe.setApiName(KEYWORDS);
                    selectOneFieldDescribe.setLabel(I18N.text("BiddingSubscriptionObj.field.keywords.label"));
                    selectOneFieldDescribe.setDefaultValue(values);
                    if (CollectionUtils.isNotEmpty(options)) {
                        newFieldDescribes.add(selectOneFieldDescribe);
                    }
                } else if (PUBLISH_TIME.equals(filterField)) {
                    fieldDescribe.setDefaultValue(getLastWeekAndNow());
                    newFieldDescribes.add(fieldDescribe);
                } else if (BIDING_END_DATE_TIME.equals(filterField) || TENDER_END_DATE_TIME.equals(filterField)) {
                    fieldDescribe.setDefaultValue(rangeData2Map.get(filterField));
                    newFieldDescribes.add(fieldDescribe);
                }
                if (!isEnterpriseSub) {
                    continue;
                }

                if (CALLER_NAMES.equals(filterField) && "1".equals(ruleData.get(ENTERPRISE_MATCH_FIELD))
                        || WINNER_NAMES.equals(filterField) && "2".equals(ruleData.get(ENTERPRISE_MATCH_FIELD))) {
                    filterValue = ruleData.get(ENTERPRISE_NAME);
                    List<String> names = getEnterpriseName(ruleData.getTenantId(), ruleData);
                    SelectOneFieldDescribe selectManyFieldDescribe = (SelectOneFieldDescribe) fieldDescribe;
                    List<ISelectOption> options = selectManyFieldDescribe.getSelectOptions();
                    for (String value : names) {
                        options.add(new SelectOption(value, value, ""));
                    }
                    selectManyFieldDescribe.setDefaultValue(names);
                    selectManyFieldDescribe.setSelectOptions(options);
                    if (CollectionUtils.isNotEmpty(options)) {
                        newFieldDescribes.add(selectManyFieldDescribe);
                    }
                }
            }
        }
        document.setFieldDescribes(newFieldDescribes);
        return document;
    }

    public List<String> getObjectDataNames(String tenantId, IObjectData objectData) {
        List<String> names = new ArrayList<>();
        Object objectApiName = objectData.get(ENTERPRISE_RELATED_OBJ);
        Object objectIds = objectData.get(ENTERPRISE_RELATED_ID);
        if (objectApiName != null && objectIds != null) {
            String[] split = objectIds.toString().split(",");
            List<String> ids = Arrays.asList(split);
            names = getObjectDataNamesDeleted(tenantId, ids, objectApiName.toString());
        }
        return names;
    }

    public List<String> getObjectDataNamesDeleted(String tenantId, List<String> ids, String apiName) {
        List<IObjectData> dataByIds = serviceFacade.findObjectDataByIdsIgnoreAll(tenantId, ids, apiName);
        // 将 dataByIds 转换为 Map，键为 ID，值为 IObjectData 对象
        Map<String, IObjectData> idToObjectDataMap = dataByIds.stream().collect(Collectors.toMap(IObjectData::getId, data -> data));
        // 使用 Stream API 处理并收集结果
        return ids.stream().map(id -> {
            IObjectData data = idToObjectDataMap.get(id);
            if (data == null) {
                return I18N.text("已删除"); // ignoreI18n
            } else if (data.get(LIFE_STATUS) != null && "invalid".equals(data.get(LIFE_STATUS))) {
                return I18N.text("sfa.has.been.invalid");
            } else if (Boolean.TRUE.equals(data.isDeleted())) {
                return I18N.text("已删除"); // ignoreI18n
            } else {
                return data.getName();
            }
        }).collect(Collectors.toList());
    }

    /**
     * 获取有效的企业名称
     *
     * @param tenantId
     * @param ids
     * @param apiName
     * @return
     */
    public List<String> getObjectDataNamesActivated(String tenantId, List<String> ids, String apiName) {
        List<String> names = Lists.newArrayList();
        List<IObjectData> dataByIds = serviceFacade.findObjectDataByIdsIgnoreAll(tenantId, ids, apiName);
        for (IObjectData data : dataByIds) {
            if (!data.isDeleted() && (data.get(LtoFieldApiConstants.LIFE_STATUS) != null && !"invalid".equals(data.get(LtoFieldApiConstants.LIFE_STATUS)))) {
                names.add(data.getName());
            }
        }
        return names;
    }

    private String getSearchApiName(String fieldApiName) {
        // 1-智能;2-精准(默认);3-模糊
        if (KEYWORDS_MATCH_MODE.equals(fieldApiName)) {
            return "match_mode";
            //匹配类型选择:1-标题;2-产品关键词(默认);3-综合(标题+产品)
        } else if (KEYWORDS_MATCH_FIELD.equals(fieldApiName)) {
            return "match_type";
        } else if (BID_SUB_TYPE.equals(fieldApiName)) {
            return BID_PROCESSES;
        }
        return fieldApiName;
    }

    private boolean isAlwaysShowFields(String field) {
        return BUDGET_INTERVAL.equals(field) || PUSH_TIME.equals(field);
    }

    private List<String> getLastWeekAndNow() {
        long now = new Date().getTime();
        long dayAgo = now - 24 * 60 * 60 * 1000;
        List<String> list = new ArrayList<>();
        list.add(String.valueOf(dayAgo));
        list.add(String.valueOf(now));
        return list;
    }

    /**
     * 根据code获取城市名称
     *
     * @param context
     * @param codeList
     * @return
     */
    public List<String> getAreaCityLabelByCode(IActionContext context, Set<String> codeList) {
        List<Map<String, String>> cityMaps = countryAreaService.batchQueryNameByCode(context, "city", codeList);
        List<String> cityLabels = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(cityMaps)) {
            for (Map<String, String> cityMap : cityMaps) {
                cityLabels.add(cityMap.get("label"));
            }
        }
        return cityLabels;
    }

    @ServiceMethod("queryCondition")
    public ProcurementConditionModule.Result queryCondition(ServiceContext context, ProcurementConditionModule.Arg arg) {
        String tenantId = context.getTenantId();
        String userId = context.getUser().getUserIdOrOutUserIdIfOutUser();
        List<WhereParam> wheres = Lists.newArrayList();
        CommonSqlUtils.addWhereParam(wheres, ObjectFieldConstantsUtil.IS_DELETED, CommonSqlOperator.EQ, Lists.newArrayList(0));
        CommonSqlUtils.addWhereParam(wheres, IObjectData.TENANT_ID, CommonSqlOperator.EQ, Lists.newArrayList(tenantId));
        CommonSqlUtils.addWhereParam(wheres, IObjectData.CREATED_BY, CommonSqlOperator.EQ, Lists.newArrayList(userId));
        CommonSqlUtils.addWhereParam(wheres, ProcurementConstants.ConditionGroup.RECORD_TYPE, CommonSqlOperator.EQ, Lists.newArrayList(arg.getRecordType()));
        CommonSqlSearchTemplate searchTemplate = new CommonSqlSearchTemplate();
        searchTemplate.setWhereParamList(wheres);
        searchTemplate.setLimit(1000);
        searchTemplate.setTableName(ProcurementConstants.ConditionGroup.TABLE);
        OrderBy priority = new OrderBy();
        priority.setFieldName(ProcurementConstants.ConditionGroup.LAST_MODIFIED_TIME);
        priority.setIsAsc(false);
        List<OrderBy> orderByList = Lists.newArrayList(priority);
        searchTemplate.setOrderByList(orderByList);
        try {
            List<Map> dataList = CommonSqlUtils.queryData(searchTemplate, context.getTenantId());
            if (QLM.equals(arg.getRecordType())){
                dataList.forEach(this::queryCondition);
            }
            return ProcurementConditionModule.Result
                    .builder().dataList(dataList)
                    .total(dataList.size())
                    .build();
        } catch (MetadataServiceException e) {
            log.error("queryCondition error e:", e);
            throw new SFABusinessException(SFAErrorCode.ACCOUNT_COMMON_ERROR);
        }
    }

    // 转换一二级行业
    private void queryCondition(Map map) {
        Object condition = map.get("condition");
        try {
            ArrayNode conditionArrayNode = (ArrayNode) jsonMapper.readTree(String.valueOf(condition));
            for (int i = 0; i < conditionArrayNode.size(); i++) {
                ObjectNode objectNode = (ObjectNode) conditionArrayNode.get(i);
                if ("caller_type".equals(objectNode.get("field_name").asText())) {
                    ObjectNode modifiedObjectNode = jsonMapper.createObjectNode();
                    modifiedObjectNode.put("field_name", "one_level_two");
                    modifiedObjectNode.put("operator", "HASANYOF");
                    // 处理一二级行业
                    ArrayNode fieldValues = jsonMapper.createArrayNode();
                    for (JsonNode jsonNode : objectNode.get("field_values")) {
                        String str = "";
                        switch (jsonNode.asText()){
                            case "2":
                                // 通信运营商
                                str = "[[\"通信运营商\",\"82\"]]"; // ignoreI18n
                                break;
                            case "3":
                                // 部队
                                str = "[[\"部队\",\"81\"]]"; // ignoreI18n
                                break;
                            case "8":
                                // 医疗
                                str = "[[\"医疗\",\"1\"],[\"医疗\",\"2\"],[\"医疗\",\"3\"],[\"医疗\",\"4\"],[\"医疗\",\"5\"],[\"医疗\",\"6\"],[\"医疗\",\"7\"],[\"医疗\",\"8\"],[\"医疗\",\"9\"],[\"医疗\",\"10\"]]"; // ignoreI18n
                                break;
                            case "9":
                                // 政府
                                str = "[[\"政府\",\"11\"],[\"政府\",\"12\"],[\"政府\",\"13\"],[\"政府\",\"14\"],[\"政府\",\"15\"],[\"政府\",\"16\"],[\"政府\",\"17\"],[\"政府\",\"18\"],[\"政府\",\"19\"],[\"政府\",\"20\"],[\"政府\",\"21\"],[\"政府\",\"22\"],[\"政府\",\"23\"],[\"政府\",\"24\"],[\"政府\",\"25\"],[\"政府\",\"26\"],[\"政府\",\"27\"],[\"政府\",\"28\"],[\"政府\",\"29\"],[\"政府\",\"30\"],[\"政府\",\"31\"],[\"政府\",\"32\"],[\"政府\",\"33\"],[\"政府\",\"34\"],[\"政府\",\"35\"],[\"政府\",\"36\"],[\"政府\",\"37\"],[\"政府\",\"38\"],[\"政府\",\"39\"],[\"政府\",\"40\"],[\"政府\",\"41\"],[\"政府\",\"42\"],[\"政府\",\"43\"],[\"政府\",\"44\"],[\"政府\",\"45\"],[\"政府\",\"46\"],[\"政府\",\"47\"],[\"政府\",\"48\"],[\"政府\",\"49\"],[\"政府\",\"50\"],[\"政府\",\"51\"],[\"政府\",\"52\"],[\"政府\",\"53\"],[\"政府\",\"54\"],[\"政府\",\"55\"],[\"政府\",\"56\"],[\"政府\",\"57\"],[\"政府\",\"58\"],[\"政府\",\"59\"],[\"政府\",\"60\"],[\"政府\",\"61\"],[\"政府\",\"62\"],[\"政府\",\"63\"]]"; // ignoreI18n
                                break;
                            case "10":
                                // 学校
                                str = "[[\"学校\",\"64\"],[\"学校\",\"65\"],[\"学校\",\"66\"],[\"学校\",\"67\"],[\"学校\",\"68\"],[\"学校\",\"69\"],[\"学校\",\"70\"],[\"学校\",\"71\"]]"; // ignoreI18n
                                break;
                            case "12":
                                // 金融企业
                                str = "[[\"金融企业\",\"73\"],[\"金融企业\",\"74\"],[\"金融企业\",\"75\"],[\"金融企业\",\"76\"],[\"金融企业\",\"77\"],[\"金融企业\",\"78\"],[\"金融企业\",\"79\"],[\"金融企业\",\"80\"]]"; // ignoreI18n
                                break;
                        }
                        if (str.isEmpty()){
                            continue;
                        }
                        ArrayNode arrayNode = (ArrayNode) jsonMapper.readTree(str);
                        for (JsonNode node : arrayNode){
                            fieldValues.add(node);
                        }
                    }
                    // 移除一级行业 新增二级行业
                    conditionArrayNode.remove(i);
                    modifiedObjectNode.set("field_values", fieldValues);
                    conditionArrayNode.insert(i, modifiedObjectNode);
                    map.put("condition",jsonMapper.writeValueAsString(conditionArrayNode));
                    return;
                }
            }
        } catch (Exception e) {
            log.warn("一级行业转二级行业异常，map:{}", map); // ignoreI18n
        }
    }

    @ServiceMethod("deleteCondition")
    public ProcurementConditionModule.Result deleteCondition(ServiceContext context, ProcurementConditionModule.Arg arg) {
        ProcurementConditionModule.Result result = new ProcurementConditionModule.Result();
        if (StringUtils.isEmpty(arg.getId())) {
            throw new ValidateException(I18N.text(SFAI18NKeyUtil.SFA_CONDITION_ARG_NOT_NULL));
        }
        String sql = "update biz_condition_group set is_deleted = 1 where tenant_id = '%s' and created_by = '%s' and id = '%s' ";
        sql = String.format(sql, context.getTenantId(), context.getUser().getUserIdOrOutUserIdIfOutUser(), SqlEscaper.pg_escape(arg.getId()));
        int value = specialTableMapper.setTenantId(context.getTenantId()).batchUpdateBySql(sql);
        if (value == 0) {
            result.setCode(500);
            result.setMsg(I18N.text(SFAI18NKeyUtil.SFA_CONDITION_DELETE_FAIL));
        }
        return result;
    }

    @ServiceMethod("saveCondition")
    public ProcurementConditionModule.Result saveCondition(ServiceContext context, ProcurementConditionModule.Arg arg) {
        //参数校验
        String tenantId = context.getTenantId();
        String userId = context.getUser().getUserIdOrOutUserIdIfOutUser();
        validate(tenantId, userId, arg);
        Map<String, Object> conditionGroup = Maps.newHashMap();
        List<Map<String, Object>> datalist = Lists.newArrayList();
        conditionGroup.put(ProcurementConstants.ConditionGroup.CONDITION, arg.getCondition());
        conditionGroup.put(ProcurementConstants.ConditionGroup.NAME, arg.getName());
        conditionGroup.put(ProcurementConstants.ConditionGroup.OBJECT_DESCRIBE_API_NAME, ProcurementConstants.ConditionGroup.API_NAME);
        conditionGroup.put(ProcurementConstants.ConditionGroup.TENANT_ID, context.getTenantId());
        conditionGroup.put(ProcurementConstants.ConditionGroup.IS_DELETED, 0);
        conditionGroup.put(ProcurementConstants.ConditionGroup.RECORD_TYPE, arg.getRecordType());
        try {
            if (StringUtils.isNotEmpty(arg.getId())) {
                String sql = "update biz_condition_group set name = '%s',last_modified_by = '%s' where tenant_id = '%s' and created_by = '%s' and id= '%s'";
                sql = String.format(sql, SqlEscaper.pg_escape(arg.getName()), userId, tenantId, userId, SqlEscaper.pg_escape(arg.getId()));
                specialTableMapper.setTenantId(context.getTenantId()).batchUpdateBySql(sql);
            } else {
                conditionGroup.put(ProcurementConstants.ConditionGroup.ID, serviceFacade.generateId());
                conditionGroup.put(ProcurementConstants.ConditionGroup.CREATED_BY, context.getUser().getUserIdOrOutUserIdIfOutUser());
                conditionGroup.put(ProcurementConstants.ConditionGroup.CREATED_TIME, System.currentTimeMillis());
                conditionGroup.put(ProcurementConstants.ConditionGroup.LAST_MODIFIED_BY, context.getUser().getUserIdOrOutUserIdIfOutUser());
                conditionGroup.put(ProcurementConstants.ConditionGroup.LAST_MODIFIED_TIME, System.currentTimeMillis());
                datalist.add(conditionGroup);
                CommonSqlUtils.insertData(context.getTenantId(), ProcurementConstants.ConditionGroup.TABLE, datalist);
            }
        } catch (MetadataServiceException e) {
            log.error("saveCondition error e:", e);
            throw new SFABusinessException(SFAErrorCode.ACCOUNT_COMMON_ERROR);
        }
        return new ProcurementConditionModule.Result();
    }

    public void validate(String tenantId, String userId, ProcurementConditionModule.Arg arg) {
        if (StringUtils.isEmpty(arg.getName()) || StringUtils.isEmpty(arg.getCondition())) {
            throw new ValidateException(I18N.text(SFAI18NKeyUtil.SFA_CONDITION_ARG_NOT_NULL));
        }
        if (arg.getName().length() > 15) {
            throw new ValidateException(I18N.text(SFAI18NKeyUtil.SFA_CONDITION_NOT_MORE_THAN));
        }
        List<WhereParam> wheres = Lists.newArrayList();
        CommonSqlUtils.addWhereParam(wheres, ObjectFieldConstantsUtil.IS_DELETED, CommonSqlOperator.EQ, Lists.newArrayList(0));
        CommonSqlUtils.addWhereParam(wheres, IObjectData.TENANT_ID, CommonSqlOperator.EQ, Lists.newArrayList(tenantId));
        CommonSqlUtils.addWhereParam(wheres, IObjectData.CREATED_BY, CommonSqlOperator.EQ, Lists.newArrayList(userId));
        CommonSqlUtils.addWhereParam(wheres, ProcurementConstants.ConditionGroup.RECORD_TYPE, CommonSqlOperator.EQ, Lists.newArrayList(arg.getRecordType()));
        CommonSqlSearchTemplate searchTemplate = new CommonSqlSearchTemplate();
        searchTemplate.setWhereParamList(wheres);
        searchTemplate.setLimit(1000);
        searchTemplate.setTableName(ProcurementConstants.ConditionGroup.TABLE);
        try {
            if (StringUtils.isEmpty(arg.getId())) {//新增限制条数
                List<Map> queryResult = CommonSqlUtils.queryData(searchTemplate, tenantId);
                if (queryResult.size() >= 10) {
                    throw new ValidateException(I18N.text(SFAI18NKeyUtil.SFA_CONDITION_SUPPORT_SAVE));
                }
            }
            CommonSqlUtils.addWhereParam(wheres, ProcurementConstants.ConditionGroup.NAME, CommonSqlOperator.EQ, Lists.newArrayList(arg.getName()));
            searchTemplate.setWhereParamList(wheres);
            List<Map> queryResult = CommonSqlUtils.queryData(searchTemplate, tenantId);
            if (!queryResult.isEmpty()) {
                throw new ValidateException(I18N.text(SFAI18NKeyUtil.SFA_CONDITION_NAME_EXIST));
            }
        } catch (MetadataServiceException e) {
            log.error("queryCondition error e:", e);
            throw new SFABusinessException(SFAErrorCode.ACCOUNT_COMMON_ERROR);
        }
    }

    @ServiceMethod("qlm_select_url")
    public BiddingQlmAplModule.UrlCheckResult qlmSelectUrl(ServiceContext context, BiddingQlmAplModule.UrlCheckArg arg) {
        if (ObjectUtils.isEmpty(arg.getDataList()) || ObjectUtils.isEmpty(arg.getUrlList())) {
            return BiddingQlmAplModule.UrlCheckResult.error("dataList or urlList is empty.");
        }
        if (arg.getDataList().size() > 100 || arg.getUrlList().size() > 20) {
            return BiddingQlmAplModule.UrlCheckResult.error("too much data. (max data:100;max url:20)");
        }
        String tenantId = context.getTenantId();
        Map<String, String> body = new HashMap<>();
        Map<Long, String> qlmBody = new HashMap<>();
        // 查询标讯对应的qlm标讯id
        List<IObjectData> objectDataList = serviceFacade.findObjectDataByIds(tenantId, arg.getDataList(), SFAPreDefineObject.ProcurementInfo.getApiName());
        if (ObjectUtils.isEmpty(objectDataList)) {
            return BiddingQlmAplModule.UrlCheckResult.error("not found object data.");
        }
        objectDataList.forEach(o -> {
            body.put(o.getId(), "");
            String qlmDataId = o.get("bid_id", String.class);
            if (ObjectUtils.isEmpty(qlmDataId) || !"2".equals(o.get("data_source", String.class))) {
                return;
            }
            qlmBody.put(Long.parseLong(qlmDataId), o.getId());
        });
        if (ObjectUtils.isEmpty(qlmBody.keySet())) {
            return BiddingQlmAplModule.UrlCheckResult.builder().code("200").body(body).build();
        }
        // 查询数据
        JSONObject request = new JSONObject();
        request.put("dataIds", Lists.newArrayList(qlmBody.keySet()));
        request.put("domains", arg.getUrlList());
        QlmProcurementService.Result qlmResult = qlmProcurementService.commonPost("/open/detail/source", request);
        try {
            Map<Long,String> qlmDataMap = JSON.parseObject(qlmResult.getData(), new TypeReference<Map<Long, String>>(){});
            qlmDataMap.keySet().forEach(o -> body.put(qlmBody.get(o),qlmDataMap.get(o)));
        }catch (Exception e){
            log.warn("qlm json error.qlmResult:{}", qlmResult);
            return BiddingQlmAplModule.UrlCheckResult.error("qlm return value error,qlm return value:" + qlmResult);
        }
        return BiddingQlmAplModule.UrlCheckResult.builder().code("200").body(body).build();
    }

    @ServiceMethod("qlm_get_token")
    public BiddingQlmAplModule.GetTokenResult qlmGetToken(ServiceContext context, BiddingQlmAplModule.GetTokenArg arg) {
        if (ObjectUtils.isEmpty(arg)) {
            return BiddingQlmAplModule.GetTokenResult.error("args is null");
        }
        if (ObjectUtils.isEmpty(arg.getDataId()) && ObjectUtils.isEmpty(arg.getBidId()) && !Boolean.TRUE.equals(arg.getOnlyToken())) {
            return BiddingQlmAplModule.GetTokenResult.error("args is empty");
        }

        BiddingQlmAplModule.GetTokenResult ret = BiddingQlmAplModule.GetTokenResult.builder().code("200").build();
        // 获取token
        String token = procurementInfoService.getQlmToken(context.getTenantId(), arg.getRefreshToken());
        if (ObjectUtils.isEmpty(token)){
            return BiddingQlmAplModule.GetTokenResult.builder().code("500").errMsg("get token is null").build();
        }
        if (Boolean.TRUE.equals(arg.getOnlyToken())) {
            ret.setToken(token);
            return ret;
        }
        String bidId;
        if (ObjectUtils.isEmpty(arg.getBidId())) {
            IObjectData objectData = serviceFacade.findObjectData(context.getUser(), arg.getDataId(), SFAPreDefineObject.ProcurementInfo.getApiName());
            if (ObjectUtils.isEmpty(objectData) || ObjectUtils.isEmpty(objectData.get("bid_id"))){
                return BiddingQlmAplModule.GetTokenResult.builder().code("500").errMsg("Invalid data").build();
            }
            bidId = objectData.get("bid_id", String.class);
        }else {
            bidId = arg.getBidId();
        }
        // 拼接url
        ret.setUrl(String.format("%s/open-pc/#/affiche-detail/%s?token=%s", QlmProcurementService.getWEB_HOST(), bidId, token));
        return ret;
    }

    // 客开接口：提交千里马医院信息任务
    @ServiceMethod("sub_hospital_task")
    public BiddingQlmAplModule.GetHospitalResult subHospitalTask(ServiceContext context, BiddingQlmAplModule.GetHospitalArg arg) {
        return qlmRequest(context, "names", arg.getHospitalList(), "/open/hospital/information/task/submit", false);
    }

    // 客开接口：查询医院信息任务状态
    @ServiceMethod("query_hospital_state")
    public BiddingQlmAplModule.GetHospitalResult queryHospitalState(ServiceContext context, BiddingQlmAplModule.GetHospitalArg arg) {
        return qlmRequest(context, "taskIds", arg.getTaskList(), "/open/hospital/information/task/query", true);
    }

    // 客开接口：查询医院信息
    @ServiceMethod("query_hospital_msg")
    public BiddingQlmAplModule.GetHospitalResult queryHospitalMsg(ServiceContext context,  BiddingQlmAplModule.GetHospitalArg arg) {
        return qlmRequest(context, "taskId", arg.getTaskId(), "/open/hospital/information/query", true);
    }

    private BiddingQlmAplModule.GetHospitalResult qlmRequest(ServiceContext context, String paramName, Object arg, String url, boolean format) {
        if (ObjectUtils.isEmpty(arg)) {
            return BiddingQlmAplModule.GetHospitalResult.builder().code("500").errMsg("arg is null").build();
        }
        QlmProcurementService.Account account = qlmProcurementService.getAccount(context.getTenantId());
        if (account == null) {
            return BiddingQlmAplModule.GetHospitalResult.builder().code("500").errMsg("account is null").build();
        }
        JSONObject request = new JSONObject();
        request.put("accountKey", account.getAccountKey());
        request.put(paramName, arg);
        QlmProcurementService.Result qlmResult = qlmProcurementService.commonPost(url, request);
        if (!qlmResult.isSuccess()) {
            return BiddingQlmAplModule.GetHospitalResult.builder().code("500").errMsg(qlmResult.getMsg()).build();
        }
        if (format) {
            try {
                List<Map<String, String>> maps = JSONObject.parseObject(qlmResult.getData(), new TypeReference<List<Map<String, String>>>() {
                });
                return BiddingQlmAplModule.GetHospitalResult.builder().code("200").body(maps).build();
            } catch (Exception e) {
                log.warn("format error! url:{}, qlmResult:{}", url, qlmResult);
            }
        }
        return BiddingQlmAplModule.GetHospitalResult.builder().code("200").body(qlmResult.getData()).build();
    }

}
