package com.facishare.crm.management.service.config;

import com.alibaba.fastjson.JSONObject;
import com.facishare.crm.openapi.Utils;
import com.facishare.crm.sfa.task.AsyncTaskProducer;
import com.facishare.crm.sfa.utilities.util.SFAI18NKeyUtil;
import com.facishare.crm.si.InnerContractService;
import com.facishare.crm.si.erpdss.model.ContractModel;
import com.facishare.enterprise.common.util.JsonUtil;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.config.ConfigService;
import com.facishare.paas.appframework.config.ConfigValueType;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.MappingRuleDocument;
import com.facishare.paas.appframework.core.model.RequestContextManager;
import com.facishare.paas.appframework.core.model.ServiceContext;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.predef.service.ObjectRecordTypeService;
import com.facishare.paas.appframework.core.predef.service.dto.objectMapping.CreateRule;
import com.facishare.paas.appframework.core.predef.service.dto.recordType.CreateRecordType;
import com.facishare.paas.appframework.metadata.domain.DomainPluginLogicService;
import com.facishare.paas.appframework.metadata.repository.model.DomainPluginInstance;
import com.facishare.paas.appframework.metadata.repository.model.DomainPluginParam;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.service.IObjectDescribeService;
import com.facishare.paas.metadata.impl.describe.FieldDescribeFactory;
import com.facishare.paas.metadata.impl.describe.RecordTypeFieldDescribe;
import com.github.trace.TraceContext;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 合同分层结构开关配置
 */
@Component
@Slf4j
public class AdditionalSaleContractBizConfigProvider extends DefaultBizConfigProvider {

    private static final String CONTRACT_TYPE = "contract_type";
    private static final String PARENT_CONTRACT_ID = "parent_contract_id";

    private static final String ADDITIONAL_RECORD_TYPE_API_NAME = "additional_default__c";
    private static final String CONTRACT_TYPE_JSON = "{\"describe_api_name\":\"SaleContractObj\",\"is_index\":true,\"is_active\":true,\"description\":\"主子合同标识\",\"is_unique\":false,\"default_value\":\"1\",\"label\":\"主子合同标识\",\"type\":\"select_one\",\"is_required\":false,\"api_name\":\"contract_type\",\"options\":[{\"not_usable\":false,\"label\":\"主合同\",\"value\":\"1\"},{\"not_usable\":false,\"label\":\"子合同\",\"value\":\"0\"}],\"define_type\":\"package\",\"help_text\":\"\",\"status\":\"released\",\"is_extend\":false}";//ignoreI18n
    private static final String PARENT_CONTRACT_ID_JSON = "{\"describe_api_name\":\"SaleContractObj\",\"description\":\"主合同编码\",\"is_unique\":false,\"type\":\"object_reference\",\"is_required\":false,\"define_type\":\"package\",\"is_index\":true,\"is_active\":true,\"default_value\":\"\",\"label\":\"主合同编码\",\"target_api_name\":\"SaleContractObj\",\"target_related_list_name\":\"related_list_sale_contract\",\"target_related_list_label\":\"附加协议\",\"action_on_target_delete\":\"set_null\",\"api_name\":\"parent_contract_id\",\"is_index_field\":true,\"help_text\":\"\",\"wheres\":[],\"status\":\"new\"}";//ignoreI18n

    @Autowired
    private AsyncTaskProducer asyncTaskProducer;
    @Autowired
    private InnerContractService innerContractService;
    @Autowired
    private ObjectRecordTypeService objectRecordTypeService;
    @Autowired
    private DomainPluginLogicService domainPluginLogicService;
    private static final String UPDATE_TAG = "contract_type_update_data";

    @Override
    public String getConfigKey() {
        return BizConfigKey.IS_OPEN_ADDITIONAL_CONTRACT.getId();
    }

    @Override
    public void validateSetConfig(User user, String key, String new_value) {
        if ("0".equals(new_value)) {
            throw new ValidateException(I18N.text(SFAI18NKeyUtil.SFA_ADDITIONAL_CONTRACT_CLOSE_OPERATION_NOT_ALLOWED));
        }
    }

    @Override
    public void setConfigValue(User user, String value, String oldValue, String key) {
        if(Objects.equals(value, oldValue) && "1".equals(value)) {
            throw new ValidateException(I18N.text(SFAI18NKeyUtil.SFA_NOT_ALLOWED_REOPEN_OPERATION));
        }
        try {
            // 1. 执行对象加字段操作
            IObjectDescribe objectDescribe = getDescribeWithSimplifiedChinese(user, Utils.SALE_CONTRACT_API_NAME);
            if(objectDescribe == null) {
                throw new ValidateException(I18N.text(SFAI18NKeyUtil.SFA_OPEN_CONTRACT_MULTI_LEVEL_STRUCTURE_FAIL));
            }
            // 2.往销售合同对象加一个业务类型（附加协议）(api_name:addition_default__c，如果存在则不添加)
            RecordTypeFieldDescribe recordTypeFieldDescribe = (RecordTypeFieldDescribe)objectDescribe.getFieldDescribe("record_type");
            if(recordTypeFieldDescribe != null && !recordTypeFieldDescribe.isOptionExist(ADDITIONAL_RECORD_TYPE_API_NAME)){
                //调用平台的接口创建业务类型，支持分配角色和布局
                addRecordType();
            }
            //重新取一遍，取最新的对象描述
            objectDescribe = getDescribeWithSimplifiedChinese(user, Utils.SALE_CONTRACT_API_NAME);
            recordTypeFieldDescribe = (RecordTypeFieldDescribe)objectDescribe.getFieldDescribe("record_type");

            IFieldDescribe additionalContractField = objectDescribe.getFieldDescribe(CONTRACT_TYPE);
            boolean needUpdate = false;
            if(additionalContractField == null) {
                IFieldDescribe additionalContractFieldDescribe = FieldDescribeFactory.newInstance(CONTRACT_TYPE_JSON);
                objectDescribe.addFieldDescribe(additionalContractFieldDescribe);
                needUpdate = true;
            }
            IFieldDescribe parentContractIdField = objectDescribe.getFieldDescribe(PARENT_CONTRACT_ID);
            if(parentContractIdField == null) {
                IFieldDescribe parentContractIdFieldDescribe = FieldDescribeFactory.newInstance(PARENT_CONTRACT_ID_JSON);
                objectDescribe.addFieldDescribe(parentContractIdFieldDescribe);
                needUpdate = true;
            }

            if(needUpdate) {
                objectDescribeService.update(objectDescribe);
            }
            //3.将业务类型打标，标记哪些是主合同业业务类型，哪些是子合同业务类型（只有新加的 "附加协议"为自合同业务类型，其他的都是主合同业务类型）
            if(recordTypeFieldDescribe != null && recordTypeFieldDescribe.getRecordTypeOptions() != null) {
                List<ContractModel.RecordTypeMapping> recordTypeModels = Lists.newArrayList();
                recordTypeFieldDescribe.getRecordTypeOptions().stream().forEach(recordTypeOption -> {
                    recordTypeModels.add(ContractModel.RecordTypeMapping.builder()
                            .apiName(recordTypeOption.getApiName())
                            .changed(false)
                            .isMain(!Objects.equals(recordTypeOption.getApiName(), ADDITIONAL_RECORD_TYPE_API_NAME))
                            .build());
                });
                configService.upsertTenantConfig(user, BizConfigKey.SALE_CONTRACT_RECORD_TYPE_MAPPING.getKey(), JsonUtil.toJson(recordTypeModels), ConfigValueType.STRING);
            }
            //4.更新映射规则
            processRuleMapping(user);
            //5.刷历史数据，将历史数据的主子合同标识设置为1，异步刷数据
            sendContractTypeUpdateDataMsg(user);
            List<String> bindingPluginObjectApiNames = Lists.newArrayList(Utils.SALE_CONTRACT_API_NAME, Utils.SALES_ORDER_API_NAME, Utils.ACCOUNT_API_NAME);
            //开启时自动绑定一个合同层级插件
            bindingPluginObjectApiNames.stream().forEach(bindingPluginObjectApiName -> createDomain(user, bindingPluginObjectApiName));
        } catch (Exception e) {
            log.error("开启销售合同分层结构失败", e);
            throw new ValidateException(I18N.text(SFAI18NKeyUtil.SFA_ADDITIONAL_CONTRACT_OPEN_FAILURE_TITLE));
        }
        super.setConfigValue(user, value, oldValue, key);
    }

    private void createDomain(User user, String apiName) {
        DomainPluginParam domainPluginParam = new DomainPluginParam();
        //主对象映射
        domainPluginParam.setFieldMapping(Maps.newHashMap());

        DomainPluginInstance instance = DomainPluginInstance.builder()
                .pluginApiName("sale_contract_component")
                .refObjectApiName(apiName)
                .recordTypeList(Lists.newArrayList())
                .pluginParam(domainPluginParam)
                .build();
        domainPluginLogicService.createPluginInstance(user, instance);
    }
    private void processRuleMapping(User user) {
        CreateRule.Arg ruleArg = new CreateRule.Arg();
        ruleArg.setDescribeApiName(Utils.SALE_CONTRACT_API_NAME);
        List<Map> fieldMap = Lists.newArrayList();
        Map accountIdMap = Maps.newHashMap();
        accountIdMap.put("source_field_api_name", "account_id");
        accountIdMap.put("target_field_api_name", "account_id");
        fieldMap.add(accountIdMap);

        Map parentContractIdMap = Maps.newHashMap();
        parentContractIdMap.put("source_field_api_name", "_id");
        parentContractIdMap.put("target_field_api_name", "parent_contract_id");
        fieldMap.add(parentContractIdMap);

        MappingRuleDocument ruleDocument = new MappingRuleDocument();
        ruleDocument.put("field_mapping", fieldMap);
        ruleDocument.put("rule_api_name", "sale_contract2sub_contract__c");
        ruleArg.setRuleList(Lists.newArrayList(ruleDocument));
        innerContractService.processContractRule(user, ruleArg, false);
    }

    private void sendContractTypeUpdateDataMsg(User user) {
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("tenantId", user.getTenantId());
        jsonObject.put("userId", user.getUserId());
        String messageKey = "contractTypeUpdateData"
                .concat("@")
                .concat(user.getTenantId());
        jsonObject.put("traceId", TraceContext.get().getTraceId());
        asyncTaskProducer.create(UPDATE_TAG, jsonObject.toJSONString(), messageKey);
    }

    private void addRecordType() {
        ServiceContext serviceContext = new ServiceContext(RequestContextManager.getContext(), null, null);
        CreateRecordType.Arg arg = new CreateRecordType.Arg();
        arg.setDescribeApiName(Utils.SALE_CONTRACT_API_NAME);
        arg.setRecord_type(String.format("{\"label\":\"%s\",\"api_name\":\"%s\",\"description\":\"\",\"is_active\":true,\"roles\":[{\"role_code\":\"00000000000000000000000000000006\",\"is_default\":false,\"is_used\":true,\"layout_api_name\":\"SaleContractObj_layout_generate_by_UDObjectServer__c\"}]}",I18N.text("SaleContractObj.field.record_type.option.additional_default__c"), ADDITIONAL_RECORD_TYPE_API_NAME));
        CreateRecordType.Result result = objectRecordTypeService.createRecordType(arg, serviceContext);
        log.info("addRecordType result:{}", result);
    }
}
